import React from 'react';
import { useTranslation } from 'react-i18next';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, TrendingUp, Target, Building, Lightbulb, DollarSign } from 'lucide-react';

interface RoleSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RoleSelectionModal: React.FC<RoleSelectionModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  const handleRoleSelection = (role: 'founder' | 'investor') => {
    onClose();
    window.location.href = `/${role}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl p-0 bg-gradient-to-br from-slate-50 to-teal-50">
        <DialogHeader className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] text-white p-8 rounded-t-lg">
          <DialogTitle className="text-3xl font-bold text-center">
            {t('roleSelection.title')}
          </DialogTitle>
          <p className="text-center text-white/90 text-lg mt-2">
            {t('roleSelection.subtitle')}
          </p>
        </DialogHeader>
        
        <div className="p-8">
          <div className="grid md:grid-cols-2 gap-8">
            {/* Founder Card */}
            <Card
              className="border-2 border-gray-200 hover:border-[#40826D] transition-all duration-300 hover:shadow-xl cursor-pointer group"
              onClick={() => handleRoleSelection('founder')}
            >
              <CardHeader className="text-center pb-4">
                <div className="w-20 h-20 bg-gradient-to-br from-[#40826D] to-[#2D5A4A] rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Lightbulb className="w-10 h-10 text-white" />
                </div>
                <CardTitle className="text-2xl text-gray-900">{t('roleSelection.founder.title')}</CardTitle>
                <p className="text-gray-600 text-lg">
                  {t('roleSelection.founder.description')}
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-[#40826D] rounded-full flex items-center justify-center">
                      <Target className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-700">{t('roleSelection.founder.feature1')}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-[#40826D] rounded-full flex items-center justify-center">
                      <TrendingUp className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-700">{t('roleSelection.founder.feature2')}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-[#40826D] rounded-full flex items-center justify-center">
                      <DollarSign className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-700">{t('roleSelection.founder.feature3')}</span>
                  </div>
                </div>
                
                <Button
                  onClick={() => handleRoleSelection('founder')}
                  className="w-full bg-gradient-to-r from-[#40826D] to-[#2D5A4A] hover:from-[#2D5A4A] hover:to-[#40826D] text-white font-semibold py-3 text-lg"
                >
                  {t('roleSelection.founder.button')}
                </Button>
              </CardContent>
            </Card>

            {/* Investor Card */}
            <Card
              className="border-2 border-gray-200 hover:border-[#40826D] transition-all duration-300 hover:shadow-xl cursor-pointer group"
              onClick={() => handleRoleSelection('investor')}
            >
              <CardHeader className="text-center pb-4">
                <div className="w-20 h-20 bg-gradient-to-br from-[#40826D] to-[#2D5A4A] rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Building className="w-10 h-10 text-white" />
                </div>
                <CardTitle className="text-2xl text-gray-900">{t('roleSelection.investor.title')}</CardTitle>
                <p className="text-gray-600 text-lg">
                  {t('roleSelection.investor.description')}
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-[#40826D] rounded-full flex items-center justify-center">
                      <Target className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-700">{t('roleSelection.investor.feature1')}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-[#40826D] rounded-full flex items-center justify-center">
                      <TrendingUp className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-700">{t('roleSelection.investor.feature2')}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-[#40826D] rounded-full flex items-center justify-center">
                      <User className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-700">{t('roleSelection.investor.feature3')}</span>
                  </div>
                </div>
                
                <Button
                  onClick={() => handleRoleSelection('investor')}
                  className="w-full bg-gradient-to-r from-[#40826D] to-[#2D5A4A] hover:from-[#2D5A4A] hover:to-[#40826D] text-white font-semibold py-3 text-lg"
                >
                  {t('roleSelection.investor.button')}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RoleSelectionModal;
